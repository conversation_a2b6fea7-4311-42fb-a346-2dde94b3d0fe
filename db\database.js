const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');
const dbDir = path.join(__dirname);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}
const dbPath = path.join(dbDir, 'streamflow.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error connecting to database:', err.message);
  } else {
    createTables();
  }
});
function createTables() {
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE,
    password TEXT NOT NULL,
    avatar_path TEXT,
    gdrive_api_key TEXT,
    role TEXT DEFAULT 'user',
    plan_type TEXT DEFAULT 'free',
    max_streaming_slots INTEGER DEFAULT 1,
    max_storage_gb INTEGER DEFAULT 5,
    used_storage_gb REAL DEFAULT 0,
    subscription_start_date TIMESTAMP,
    subscription_end_date TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating users table:', err.message);
    }
  });
  db.run(`CREATE TABLE IF NOT EXISTS videos (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    filepath TEXT NOT NULL,
    thumbnail_path TEXT,
    file_size INTEGER,
    duration REAL,
    format TEXT,
    resolution TEXT,
    bitrate INTEGER,
    fps TEXT,
    codec TEXT,
    audio_codec TEXT,
    user_id TEXT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating videos table:', err.message);
    }

    // Add codec columns if they don't exist (migration)
    db.run(`ALTER TABLE videos ADD COLUMN codec TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding codec column:', err.message);
      }
    });

    db.run(`ALTER TABLE videos ADD COLUMN audio_codec TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding audio_codec column:', err.message);
      }
    });
  });
  db.run(`CREATE TABLE IF NOT EXISTS streams (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    video_id TEXT,
    rtmp_url TEXT NOT NULL,
    stream_key TEXT NOT NULL,
    platform TEXT,
    platform_icon TEXT,
    bitrate INTEGER DEFAULT 2500,
    resolution TEXT,
    fps INTEGER DEFAULT 30,
    orientation TEXT DEFAULT 'horizontal',
    loop_video BOOLEAN DEFAULT 1,
    schedule_time TIMESTAMP,
    duration INTEGER,
    status TEXT DEFAULT 'offline',
    status_updated_at TIMESTAMP,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    use_advanced_settings BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (video_id) REFERENCES videos(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating streams table:', err.message);
    }
  });
  db.run(`CREATE TABLE IF NOT EXISTS stream_history (
    id TEXT PRIMARY KEY,
    stream_id TEXT,
    title TEXT NOT NULL,
    platform TEXT,
    platform_icon TEXT,
    video_id TEXT,
    video_title TEXT,
    resolution TEXT,
    bitrate INTEGER,
    fps INTEGER,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration INTEGER,
    use_advanced_settings BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (stream_id) REFERENCES streams(id),
    FOREIGN KEY (video_id) REFERENCES videos(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating stream_history table:', err.message);
    }
  });

  // Create subscription plans table
  db.run(`CREATE TABLE IF NOT EXISTS subscription_plans (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    price REAL NOT NULL,
    currency TEXT DEFAULT 'USD',
    billing_period TEXT DEFAULT 'monthly',
    max_streaming_slots INTEGER DEFAULT 1,
    max_storage_gb INTEGER DEFAULT 5,
    features TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating subscription_plans table:', err.message);
    }
  });

  // Create user subscriptions table
  db.run(`CREATE TABLE IF NOT EXISTS user_subscriptions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    plan_id TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    payment_method TEXT,
    payment_id TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating user_subscriptions table:', err.message);
    }
  });

  // Create role permissions table
  db.run(`CREATE TABLE IF NOT EXISTS role_permissions (
    id TEXT PRIMARY KEY,
    role TEXT NOT NULL,
    permission TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating role_permissions table:', err.message);
    }
  });

  // Insert default subscription plans
  insertDefaultPlans();
  insertDefaultPermissions();
}
function insertDefaultPlans() {
  const { v4: uuidv4 } = require('uuid');

  const defaultPlans = [
    {
      id: uuidv4(),
      name: 'Free',
      price: 0,
      currency: 'USD',
      billing_period: 'monthly',
      max_streaming_slots: 1,
      max_storage_gb: 5,
      features: JSON.stringify(['1 Streaming Slot', '5GB Storage', 'Basic Support'])
    },
    {
      id: uuidv4(),
      name: 'Basic',
      price: 9.99,
      currency: 'USD',
      billing_period: 'monthly',
      max_streaming_slots: 3,
      max_storage_gb: 25,
      features: JSON.stringify(['3 Streaming Slots', '25GB Storage', 'Priority Support', 'HD Streaming'])
    },
    {
      id: uuidv4(),
      name: 'Pro',
      price: 19.99,
      currency: 'USD',
      billing_period: 'monthly',
      max_streaming_slots: 10,
      max_storage_gb: 100,
      features: JSON.stringify(['10 Streaming Slots', '100GB Storage', '24/7 Support', 'HD/4K Streaming', 'Advanced Analytics'])
    },
    {
      id: uuidv4(),
      name: 'Enterprise',
      price: 49.99,
      currency: 'USD',
      billing_period: 'monthly',
      max_streaming_slots: -1, // Unlimited
      max_storage_gb: 500,
      features: JSON.stringify(['Unlimited Streaming Slots', '500GB Storage', 'Dedicated Support', 'Custom Features', 'API Access'])
    }
  ];

  defaultPlans.forEach(plan => {
    db.run(
      `INSERT OR IGNORE INTO subscription_plans
       (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [plan.id, plan.name, plan.price, plan.currency, plan.billing_period,
       plan.max_streaming_slots, plan.max_storage_gb, plan.features],
      (err) => {
        if (err) {
          console.error('Error inserting default plan:', err.message);
        }
      }
    );
  });
}

function insertDefaultPermissions() {
  const { v4: uuidv4 } = require('uuid');

  const defaultPermissions = [
    // Admin permissions
    { role: 'admin', permission: 'manage_users' },
    { role: 'admin', permission: 'manage_plans' },
    { role: 'admin', permission: 'view_all_streams' },
    { role: 'admin', permission: 'manage_system' },
    { role: 'admin', permission: 'unlimited_streaming' },
    { role: 'admin', permission: 'unlimited_storage' },

    // User permissions
    { role: 'user', permission: 'create_stream' },
    { role: 'user', permission: 'upload_video' },
    { role: 'user', permission: 'view_own_streams' },
    { role: 'user', permission: 'manage_profile' },

    // Moderator permissions
    { role: 'moderator', permission: 'view_all_streams' },
    { role: 'moderator', permission: 'moderate_content' },
    { role: 'moderator', permission: 'manage_profile' }
  ];

  defaultPermissions.forEach(perm => {
    db.run(
      `INSERT OR IGNORE INTO role_permissions (id, role, permission) VALUES (?, ?, ?)`,
      [uuidv4(), perm.role, perm.permission],
      (err) => {
        if (err) {
          console.error('Error inserting default permission:', err.message);
        }
      }
    );
  });
}

function checkIfUsersExist() {
  return new Promise((resolve, reject) => {
    db.get('SELECT COUNT(*) as count FROM users', [], (err, result) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(result.count > 0);
    });
  });
}

module.exports = {
  db,
  checkIfUsersExist
};